<view class="notice-list">
  <view class="notice-list-item" wx:for="{{list}}" wx:key="index" bindtap="goDetail" data-item="{{item}}">
    <view class="title text-ellipsis-2">
      <image class="is-top" mode="widthFix" wx:if="{{item.is_top==1}}" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/common/job_is_top.png"></image>
      {{item.title}}
    </view>
    <view class="bottom" wx:if="{{item.suit_job_num || item.release_time || item.apply_status || item.need_num || item.job_num}}">
      <view class="flex-v">
        <block wx:if="{{item.apply_status.text}}">
          <text class="status blue" style="color:{{item.apply_status.color}}">{{item.apply_status.text}}</text>
          <view class="line" wx:if="{{item.need_num || item.job_num}}">｜</view>
        </block>
        <view class="item-text mr16" wx:if="{{item.need_num>0}}">共招<text class="num">{{item.need_num<0?'若干':item.need_num}}</text>人</view>
        <view class="item-text" wx:if="{{item.job_num>0}}"><text class="num">{{item.job_num}}</text>个职位</view>
      </view>
      <view class="item-text" wx:if="{{item.suit_job_num>0}}">适合职位<text class="num">{{item.suit_job_num}}</text>个</view>
      <view class="time" wx:else>{{item.release_time}}</view>
    </view>
  </view>
</view>