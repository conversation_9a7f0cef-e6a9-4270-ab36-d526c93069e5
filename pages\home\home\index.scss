page {
  box-sizing: border-box;
  background: rgba(247, 248, 250, 1);
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
}

/* 页面容器 */
.page-container {
  box-sizing: border-box;
  min-height: 100vh;
  width: 100%;
  position: relative;
  // background-image: url(https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/common/banner_bg.png);
  background-image: url(https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/common/job_home_bg.png);
  background-repeat: no-repeat;
  background-size: 100%;

  /* 当 activeIndex 为 1 时移除背景图片 */
  &.no-bg {
    background-image: none;
  }

  /* 禁用滚动时的样式 */
  &.scroll-disabled {
    position: fixed;
    left: 0;
    right: 0;
    width: 100%;
    /* 保持原有的背景和样式 */
    background: rgba(247, 248, 250, 1);
    // background-image: url(https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/common/banner_bg.png);
    background-image: url(https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/common/job_home_bg.png);
    background-repeat: no-repeat;
    background-size: 100%;

    /* 当同时有 no-bg 类时移除背景图片 */
    &.no-bg {
      background-image: none;
    }

    /* 当弹窗显示时，控制notice-card背景图的显示 */
    .notice-list-item {
      background-image: none !important;
      background: #ffffff !important;
    }
  }
}

.tab-list {
  display: flex;
  align-items: center;
  padding-left: 32rpx;

  .tab-list-item {
    font-size: 32rpx;
    color: rgba(102, 102, 102, 1);
    margin-right: 80rpx;
    padding-bottom: 20rpx;
    .titie_img {
      // height: 36rpx;
      display: block;
    }
    .line {
      width: 88rpx;
      height: 16rpx;
      position: absolute;
      left: 0;
      bottom: 18rpx;
    }
    .line1 {
      width: 160rpx;
      height: 16rpx;
      position: absolute;
      left: -10rpx;
      bottom: 18rpx;
    }
    // padding: 20rpx 0;
    &.active {
      font-size: 36rpx;
      position: relative;
      font-weight: 600;
      color: rgba(34, 36, 46, 1);

      // &::after {
      //   position: absolute;
      //   display: block;
      //   content: " ";
      //   width: 32rpx;
      //   height: 6rpx;
      //   background-color: #fff;
      //   bottom: 3rpx;
      //   left: 50%;
      //   transform: translateX(-50%);
      //   border-radius: 20px;
      // }
    }
  }
}

.selected-box {
  display: flex;
  align-items: center;
  padding: 0 32rpx;
  margin-top: 15rpx;

  .lefts {
    width: 540rpx;
    height: 272rpx;
    position: relative;
    border-radius: 16rpx;
    overflow: hidden;
  }

  .left {
    background: linear-gradient(177deg, #ffe6e6 0%, #ffffff 100%);
    width: 540rpx;
    height: 272rpx;
    box-sizing: border-box;
    padding: 24rpx;
    padding-bottom: 16rpx;

    .left-top {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding-bottom: 40rpx;

      &.line-bottom {
        padding-bottom: 26rpx;
        border-bottom: 1px solid rgba(235, 236, 240, 1);
      }

      .title-img {
        width: 120rpx;
        height: 30rpx;
      }

      .more-box {
        display: flex;
        align-items: center;
        font-size: 24rpx;
        color: rgba(102, 102, 102, 1);

        .arrow {
          width: 24rpx;
          height: 24rpx;
          margin-top: 2rpx;
        }
      }
    }

    .swiper-list {
      padding-top: 25rpx;
    }

    .collection-box {
      padding-top: 26rpx;

      .title {
        display: flex;
        align-items: center;

        .text {
          font-size: 26rpx;
          color: rgba(34, 36, 46, 1);
          font-weight: 600;
        }

        .img {
          width: 6rpx;
          height: 25rpx;
          margin-right: 16rpx;
        }
      }

      .menu-collect {
        margin-top: 20rpx;
        display: flex;
        justify-content: space-between;

        .menu-collect-item {
          flex: 1;

          .text {
            font-size: 22rpx;
            color: rgba(145, 148, 153, 1);
          }

          .label {
            margin-top: 2rpx;
            font-size: 20rpx;
            color: rgba(34, 36, 46, 1);

            .num {
              font-size: 26rpx;
              color: rgba(34, 36, 46, 1);
              font-weight: 600;
            }
          }
        }
      }
    }

    .notice-top {
      margin-top: 26rpx;

      .title {
        font-size: 26rpx;
        color: rgba(34, 36, 46, 1);
        line-height: 40rpx;
        font-weight: 600;

        .img {
          width: 6rpx;
          height: 25rpx;
        }
      }

      .time {
        margin-top: 14rpx;
        font-size: 22rpx;
        color: rgba(194, 197, 204, 1);
      }
    }
  }

  .right {
    margin-left: 18rpx;

    .menu-item {
      width: 128rpx;
      height: 128rpx;
      background: linear-gradient(162deg, #ffefd8 0%, #ffffff 100%);
      border-radius: 16rpx;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      margin-bottom: 16rpx;
      box-sizing: border-box;

      &:last-child {
        margin-bottom: 0;
        background: linear-gradient(165deg, #ddebff 0%, #ffffff 100%);
      }

      .menu-img {
        width: 48rpx;
        height: 48rpx;
      }

      .word {
        font-size: 20rpx;
        margin-top: 12rpx;
        color: rgba(60, 61, 66, 1);
      }
    }
  }
}

.swiper {
  height: 272rpx;
  box-sizing: border-box;
  // margin-top: 30rpx;
  border-radius: 16rpx;

  .swiper-item {
    display: flex;
    align-items: center;
    margin-bottom: 20rpx;

    &:last-child {
      margin-bottom: 0;
    }

    .number {
      font-size: 26rpx;
      color: rgba(60, 61, 66, 1);

      .num {
        color: rgba(230, 0, 3, 1);
        margin: 0 4rpx;
      }
    }

    .text {
      font-size: 26rpx;
      color: rgba(60, 61, 66, 1);
      padding-right: 40rpx;
      flex: 1;
      min-width: 0;
    }
  }
}

.custom-indicator {
  position: absolute;
  bottom: 16rpx;
  left: 50%;

  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 16rpx;

  .indicator-dot {
    width: 4rpx;
    height: 4rpx;
    background: rgba(226, 175, 136, 0.4);
    border-radius: 6rpx;
    margin: 0 4rpx;

    &.active {
      width: 8rpx;
      background: rgba(226, 175, 136, 1);
    }
  }
}

/* 独立的scroll-view容器样式 */
.announcement-scroll-container,
.news-scroll-container {
  height: 100vh;
  width: 100%;
  position: relative;
  z-index: 1;

  /* 确保scroll-view能够正确滚动 */
  box-sizing: border-box;

  /* 当页面滚动被禁用时，禁用scroll-view的滚动 */
  .page-container.scroll-disabled & {
    overflow: hidden;
    pointer-events: none;
  }
}

.main-content {
  position: relative;
  z-index: 2;
  // margin-top: 32rpx;
  // background: rgba(247, 248, 250, 1);
  // padding: 40rpx 32rpx;
  padding-bottom: 40rpx;

  // padding-top: 24rpx;
  box-sizing: border-box;

  &.mt0 {
    margin-top: 0;
  }

  .main-top {
    position: sticky;
    top: var(--header-height, 100px);
    // border-radius: 24rpx 24rpx 0 0;
    padding: 12rpx 32rpx 24rpx 32rpx;
    box-sizing: border-box;
    background: #fff;
    z-index: 1;

    /* 确保弹窗可以显示在容器外 */
    &.bgf {
      border-radius: 24rpx 24rpx 0 0;
      background: rgba(255, 255, 255, 1);
      // padding-top: 24rpx !important;
      transform: rotateZ(360deg);
    }
  }

  /* 菜单容器 */
  .menu-container {
    position: relative;
    overflow: visible;
  }

  .img-bg {
    width: 100%;
  }

  .content-box {
    padding: 0 32rpx;
    background: rgba(247, 248, 250, 1);
    padding-top: 40rpx;
  }
}

/* 全局弹窗和遮罩层样式已移到组件中 */

.search {
  width: 40rpx;
  height: 40rpx;
}

.bgf {
  .tab-list-item {
    color: rgba(60, 61, 66, 1);

    &.active {
      color: rgba(60, 61, 66, 1);
      font-weight: 600;

      &::after {
        background: rgba(230, 0, 3, 1);
        border-radius: 5rpx;
      }
    }
  }
}

.news-list {
  &-item {
    background-color: rgba(255, 255, 255, 1);
    border-radius: 16rpx;
    padding: 32rpx;
    margin-bottom: 24rpx;

    &:last-child {
      margin-bottom: 0;
    }

    .title {
      font-size: 32rpx;
      color: rgba(34, 36, 46, 1);
      line-height: 48rpx;
      font-weight: 600;
    }

    .time {
      font-size: 24rpx;
      color: rgba(145, 148, 153, 1);
      text-align: right;
      margin-top: 24rpx;
    }
  }
}

.popu-content {
  .popu-content-c {
    padding: 0 32rpx;
    padding-top: 24rpx;
    padding-bottom: 8rpx;
    background: rgba(255, 255, 255, 1);

    .exam-list {
      display: flex;
      flex-wrap: wrap;

      // padding-top: 24rpx;
      &-item {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 212rpx;
        height: 72rpx;
        font-size: 26rpx;
        background: rgba(235, 236, 240, 0.6);
        border-radius: 12rpx;
        margin-right: 24rpx;
        margin-bottom: 24rpx;

        &:nth-child(3n) {
          margin-right: 0;
        }

        &.active {
          color: rgba(230, 0, 3, 1);
          background-color: rgba(230, 0, 3, 0.05);
        }
      }
    }
  }
}

.customer-box {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  box-shadow: 0rpx 8rpx 24rpx 2rpx rgba(34, 36, 46, 0.15);
  background: #ffffff;
  border: 1rpx solid #ebecf0;
  transform: rotateZ(360deg);
  position: fixed;
  bottom: 14%;
  right: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  z-index: 99;

  .img {
    width: 40rpx;
    height: 40rpx;
  }

  font-size: 22rpx;
  color: rgba(60, 61, 66, 1);
}

// 地区列表样式
.region-list-container {
  padding-top: 24rpx;
  position: relative;
  padding-right: 50rpx;

  .region-list-ul {
    display: flex;
    white-space: nowrap;

    .region-list-item {
      background: rgba(235, 236, 240, 0.6);
      border-radius: 26rpx;
      font-size: 22rpx;
      color: #666;
      height: 52rpx;
      padding: 0 32rpx;
      display: inline-flex;
      align-items: center;
      margin-right: 16rpx;

      &.active {
        background: rgba(230, 0, 3, 0.05);
        color: rgba(230, 0, 3, 1);
        font-weight: 600;
      }
    }
  }

  .right-box {
    padding-top: 24rpx;
    position: absolute;
    padding-left: 22rpx;
    background: rgba(255, 255, 255, 1);
    display: flex;
    right: 0;
    top: 0;
    height: 100%;

    .add-img {
      width: 35rpx;
      height: 35rpx;
      margin-top: 8rpx;
    }
  }
}

.top-box {
  padding: 0 32rpx;
  padding-top: 32rpx;
  box-sizing: border-box;
  // background: #fff;
  .menu-box {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 8rpx;
    box-sizing: border-box;
    &-item {
      display: flex;
      flex-direction: column;
      font-size: 24rpx;
      color: rgba(60, 61, 66, 1);
      justify-content: center;
      align-items: center;
      box-sizing: border-box;
      .img {
        width: 64rpx;
        height: 64rpx;
        margin-bottom: 14rpx;
      }
    }
  }
}
.top-card-list {
  padding: 0 32rpx;
  background: #fff;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 18rpx;
  margin-top: 40rpx;
  box-sizing: border-box;
  // padding-bottom: 36rpx;
  .top-card-item {
    padding: 40rpx 32rpx 32rpx 32rpx;
    position: relative;
    box-sizing: border-box;
    // background-repeat: no-repeat;
    // background-size: 100%;
    flex: 1;
    height: 238rpx;
    border-radius: 16rpx;
    .bg-img {
      position: absolute;
      width: 180rpx;
      height: 238rpx;
      right: 0;
      top: 0;
      z-index: 1;
      border-radius: 16rpx;
    }
    .bg-imgs {
      position: absolute;
      width: 100%;
      height: 100%;
      right: 0;
      top: 0;
      z-index: 1;
      border-radius: 16rpx;
    }
    .pr-box {
      position: relative;
      z-index: 2;
    }
    .title {
      font-size: 30rpx;
      color: rgba(60, 61, 66, 1);
      font-weight: 600;
    }
    .position-text {
      display: flex;
      align-items: center;
      margin-top: 8rpx;
    }
    .label-img {
      width: 52rpx;
      height: 30rpx;
      position: absolute;
      top: 0;
      left: 24rpx;
      z-index: 2;
    }
    .bottom-text {
      margin-top: 54rpx;
    }
    .mt8 {
      margin-top: 8rpx;
    }
    .text-item {
      font-size: 24rpx;
      color: rgba(145, 148, 153, 1);
      margin-right: 16rpx;
      &:last-child {
        margin-right: 0;
      }
      .num {
        font-size: 24rpx;
        color: rgba(102, 102, 102, 1);
        margin: 0 4rpx;
      }
    }
  }
}

.swiper-box {
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  z-index: 2;
  display: flex;
  flex-direction: column;
  padding: 4rpx;
  .title-box {
    display: flex;
    align-items: center;
    padding: 24rpx 24rpx 20rpx 24rpx;
    .title-img {
      width: 120rpx;
      height: 32rpx;
    }
    .arrow-img {
      width: 32rpx;
      height: 32rpx;
      margin-left: 4rpx;
    }
  }
  .content-box {
    background: #fff;
    flex: 1;
    min-height: 0;
    border-radius: 16rpx 0rpx 12rpx 12rpx;
    position: relative;
    padding: 20rpx 20rpx 0 20rpx;
    .jiao {
      position: absolute;
      right: 0;
      top: -24rpx;
      width: 24rpx;
      height: 24rpx;
    }
    .swiper {
      height: 100%;
      .text-box {
        .title {
          font-size: 26rpx;
          color: rgba(60, 61, 66, 1);
          line-height: 36rpx;
          .label-imgs {
            width: 48rpx;
            height: 26rpx;
            margin-right: 4rpx;
            transform: translateY(4rpx);
          }
        }
        .label {
          font-size: 22rpx;
          color: rgba(145, 148, 153, 1);
          margin-top: 6rpx;
          .num {
            color: rgba(60, 61, 66, 1);
            margin: 0 4rpx;
          }
        }
      }
    }
  }
}

.pt0 {
  padding-top: 0 !important;
}

.line-s {
  height: 8rpx;
  background: #f7f8fa;
}

.subscribe-box {
  position: relative;
  box-sizing: border-box;
  background: #fff;
  padding: 0 32rpx 12rpx 32rpx;
  width: 100%;
  margin-top: 36rpx;
  .close-box {
    position: absolute;
    right: 32rpx;
    top: 0;
    padding-left: 10rpx;
    .close {
      width: 24rpx;
      height: 24rpx;
      margin-right: 8rpx;
    }
  }

  .img {
    width: 100%;
    display: block;
  }
}
